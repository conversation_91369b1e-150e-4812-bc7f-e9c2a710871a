/* Consolidated Common Styles for the entire application */

/* ===== Table Styling ===== */
.table {
  font-size: 0.75rem;
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
  border-collapse: collapse;
  border: 1px solid #dee2e6;
}

/* Table Header Styling */
.table thead th,
.sortable-header {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-bottom: 2px solid #1086be;
  font-weight: 600;
  color: #1086be;
  white-space: nowrap;
  padding: 0.75rem 0.5rem;
  font-size: 0.8rem;
  text-transform: capitalize;
  position: relative;
  vertical-align: middle;
  text-align: left;
}

/* Table Header Bottom Border */
.table thead th::after,
.sortable-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1086be;
}

/* Sortable Header Styling */
.sortable-header {
  user-select: none;
  transition: background-color 0.2s;
}

.sortable-header:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sortable-header.active {
  background-color: rgba(0, 123, 255, 0.1);
}

/* Sort icons */
.sortable-header i.fas {
  font-size: 0.8rem;
}

.sortable-header i.fa-sort-up,
.sortable-header i.fa-sort-down {
  color: #007bff;
}

/* Table Cell Styling */
.table tbody td {
  padding: 0.4rem 0.5rem;
  vertical-align: middle;
  border: 1px solid #dee2e6;
}

/* Table Row Hover Effect */
.table-hover tbody tr:hover {
  background-color: rgba(16, 134, 190, 0.05);
}

/* Alternating Row Colors */
.table-hover tbody tr:nth-of-type(even) {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Center all content in cells */
.table td, .table th {
  text-align: left;
  vertical-align: middle;
}

/* Center images within table cells */
.table td img {
  display: block;
  margin: 0 auto;
}

/* ===== Action Buttons Styling ===== */
.btn-outline-primary,
.btn-outline-info,
.btn-outline-success,
.btn-outline-secondary,
.btn-outline-danger,
.btn-sm[class*="btn-outline-"] {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 3px;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Center icons within buttons */
.btn i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin: 0;
}

/* Button Colors and Hover Effects */
.btn-outline-primary {
  color: #1086be;
  border-color: #1086be;
}

.btn-outline-primary:hover {
  background-color: #1086be;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(16, 134, 190, 0.3);
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:hover {
  background-color: #17a2b8;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

.btn-outline-success {
  color: #f06b25;
  border-color: #f06b25;
}

.btn-outline-success:hover {
  background-color: #f06b25;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(240, 107, 37, 0.3);
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Action Column Styling */
.table td .d-flex.justify-content-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
}

/* ===== Status Badge Styling ===== */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25em 0.6em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.bg-success {
  background-color: green !important;
  color: white;
}

.bg-warning {
  background-color: #ffc107;
  color: #212529;
}

.bg-danger {
  background-color: #eb4141 !important;
  color: white;
}

.bg-info {
  background-color: #17a2b8;
  color: white;
}

.bg-primary {
  background-color: #1086be;
  color: white;
}

.bg-secondary {
  background-color: #6c757d;
  color: white;
}

/* ===== Export Buttons Styling ===== */
.export-btn {
  background-color: #f06b25;
  color: white;
  border-color: #f06b25;
  margin-right: 5px;
  border-radius: 15px;
  padding: 3px 10px;
  font-size: 0.75rem;
  height: 28px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(240, 107, 37, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 5px;
}

.export-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
  z-index: -1;
}

.export-btn:hover {
  background-color: #e05a15;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(240, 107, 37, 0.4);
  color: #fff;
}

.export-btn:hover::before {
  left: 100%;
}

.export-btn i {
  margin-right: 3px;
}

/* ===== Search Button Styling ===== */
.search-btn {
  background-color: #f06b25;
  color: white;
  border-color: #f06b25;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  width: 38px;
}

.search-btn:hover {
  background-color: #e05a15;
}

.input-group-sm .input-group-append {
  display: flex;
}

/* ===== Pagination Styling ===== */
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.pagination .page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.pagination .page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.pagination .page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #1086be;
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: rgba(240, 107, 37, 0.2);
  border-color: rgba(240, 107, 37, 0.5);
  color: #f06b25;
  z-index: 2;
}

.pagination .page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #f06b25;
  border-color: #f06b25;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6;
}


/* Column Selector Dropdown Styling */
.column-selector {
  max-height: 350px;
  overflow-y: auto;
  padding: 0;
  min-width: 260px;
  border: none;
  border-radius: 10px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* Dropdown Header */
.column-selector-header {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #f06b25;
  font-size: 0.9rem;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.column-selector-header i {
  color: #f06b25;
  font-size: 0.85rem;
}

/* Dropdown Content */
.column-selector-content {
  padding: 8px 0;
}

.column-selector .dropdown-item {
  padding: 8px 16px;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.column-selector .dropdown-item:hover {
  background-color: rgba(240, 107, 37, 0.05);
  border-left-color: rgba(240, 107, 37, 0.3);
}

.column-selector .dropdown-item.active {
  background-color: rgba(240, 107, 37, 0.08);
  border-left-color: #f06b25;
}

.column-selector .form-check {
  display: flex;
  align-items: center;
  margin: 0;
}

/* Custom Checkbox Styling */
.column-selector .form-check-input {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  cursor: pointer;
  border: 2px solid #dee2e6;
  position: relative;
  background-color: white;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.column-selector .form-check-input:hover {
  border-color: #f06b25;
  box-shadow: 0 0 0 1px rgba(240, 107, 37, 0.2);
}

.column-selector .form-check-input:checked {
  background-color: #f06b25;
  border-color: #f06b25;
  position: relative;
}

.column-selector .form-check-input:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.column-selector .form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(240, 107, 37, 0.25);
  outline: none;
}

.column-selector .form-check-label {
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  color: #495057;
  flex: 1;
}

/* Column Groups */
.column-group {
  border-bottom: 1px solid #f1f3f9;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.column-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.column-group-title {
  padding: 6px 16px;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Column Selector Button */
.column-selector-btn {
  background-color: #f06b25;
  color: white;
  border: none;
  padding: 5px 12px;
  font-size: 0.75rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  height: 28px;
  box-shadow: 0 2px 5px rgba(240, 107, 37, 0.3);
}

.column-selector-btn:hover,
.column-selector-btn:focus {
  background-color: #e05a15;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(240, 107, 37, 0.4);
}

.column-selector-btn i {
  font-size: 0.8rem;
}

/* Footer with actions */
.column-selector-footer {
  padding: 10px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.column-selector-footer button {
  font-size: 0.75rem;
  padding: 4px 10px;
  border-radius: 4px;
}

.btn-reset {
  color: #6c757d;
  background-color: transparent;
  border: 1px solid #ced4da;
}

.btn-reset:hover {
  background-color: #f8f9fa;
}

.btn-apply {
  background-color: #f06b25;
  color: white;
  border: none;
}

.btn-apply:hover {
  background-color: #e05a15;
  color: white;
}


/* Styles for lead links in the lead report */

/* Style for lead ID and business name links */
.lead-link {
  color: #007bff !important;
  text-decoration: none !important;
  font-weight: normal;
}

/* Hover effect for links */
.lead-link:hover {
  opacity: 0.8;
}
