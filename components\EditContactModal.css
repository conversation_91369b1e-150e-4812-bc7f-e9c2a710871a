
.input-group {
  display: flex;
  position: relative;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 4px 0 0 4px;
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.alert {
  position: relative;
  padding: 12px 20px;
  margin-bottom: 16px;
  border: 1px solid transparent;
  border-radius: 4px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #ff7f50;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.loading-overlay {
  background-color: transparent;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-group {
    flex: 0 0 100%;
  }

  .contact-modal {
    width: 95%;
    max-height: 95vh;
  }
}
